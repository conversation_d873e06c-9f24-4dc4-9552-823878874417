import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import 'notification_item.dart';

class RightPanel extends StatelessWidget {
  const RightPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Container(
      width: 320,
      color: Colors.black,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          left: BorderSide(color: AppTheme.borderColor),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Credit card section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.balance,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                  Icon(
                    Icons.add,
                    color: AppTheme.textColorSecondary,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                height: 180,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Color(0xFF43009B), Color(0xFF226FCA)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Stack(
                  children: [
                    // Card background pattern
                    Positioned.fill(
                      child: Image.asset(
                        'images/card-background-pattern.svg',
                        fit: BoxFit.cover,
                      ),
                    ),
                    
                    // Card header
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          width: 130,
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(10),
                              bottomRight: Radius.circular(10),
                            ),
                          ),
                          child: Text(
                            localizations.creditCard,
                            style: Theme.of(context).textTheme.bodySmall,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    
                    // Card logo
                    Positioned(
                      top: 15,
                      left: 7,
                      child: Image.asset(
                        'images/card-logo.svg',
                        width: 26,
                        height: 16,
                      ),
                    ),
                    
                    // Card chip
                    Positioned(
                      top: 21,
                      right: 21,
                      child: Image.asset(
                        'images/card-chip.svg',
                        width: 25,
                        height: 15,
                      ),
                    ),
                    
                    // Card number
                    Positioned(
                      top: 75,
                      left: 0,
                      right: 0,
                      child: Text(
                        '**** **** **** 2859',
                        style: Theme.of(context).textTheme.headlineMedium,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    
                    // Valid thru
                    Positioned(
                      bottom: 34,
                      right: 20,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6.6),
                          border: Border.all(
                            color: AppTheme.borderColor,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          '${localizations.validThru} 06/24',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Card details section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                localizations.cardDetails,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 9),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.cardNumber,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                  Text(
                    '****',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.balance,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                  Text(
                    '\$28,678.65',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.currency,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                  Text(
                    'USD',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.statusCard,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                  Text(
                    '06/24 (${localizations.active})',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Card navigation
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: AppTheme.textColorSecondary,
                  size: 16,
                ),
                onPressed: () {},
              ),
              Text(
                '1 ${localizations.of} 4',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textColorSecondary,
                    ),
              ),
              IconButton(
                icon: Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textColorSecondary,
                  size: 16,
                ),
                onPressed: () {},
              ),
            ],
          ),
          
          const Divider(),
          
          // Notifications section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.notifications,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  Text(
                    localizations.seeAll,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textColorSecondary,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              
              // Notification items
              NotificationItem(
                avatar: 'images/contact-avatar-1.png',
                name: 'Hola Spine',
                action: localizations.preparedAReport,
                timeAgo: '2${localizations.minutesAgo}',
              ),
              NotificationItem(
                avatar: 'images/contact-avatar-2.png',
                name: 'Eva Solain',
                action: localizations.invitedYouToAChat,
                timeAgo: '5${localizations.minutesAgo}',
              ),
              NotificationItem(
                avatar: 'images/contact-avatar-3.png',
                name: 'Pierre Ford',
                action: localizations.invitedYouToAMeeting,
                timeAgo: '15${localizations.minutesAgo}',
              ),
              NotificationItem(
                avatar: 'images/contact-avatar-4.png',
                name: 'Steve Ater',
                action: localizations.invitedYouToAChat,
                timeAgo: '1${localizations.daysAgo}',
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Unread notifications
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '2 ${localizations.unread}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textColorSecondary,
                    ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.cardBackgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  localizations.markAllAsRead,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textColor,
                      ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
