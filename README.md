# Bilingual Financial Dashboard

A bilingual (English and Arabic) financial dashboard with RTL support built with Flutter.

## Features

- Bilingual support (English and Arabic)
- RTL layout support for Arabic
- Dark theme
- Responsive design
- Interactive dashboard with cards and charts
- Localization using <PERSON><PERSON><PERSON>'s intl package
- State management with Provider

## Getting Started

### Prerequisites

- Flutter SDK (>=3.1.3)
- Dart SDK (>=3.1.3)

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   flutter pub get
   ```
3. Run the app:
   ```
   flutter run
   ```

### Assets

Make sure to place all the required assets in the appropriate directories:

- Images: Place all SVG and PNG files in the `images/` directory
- Fonts: Place the Inter font files in the `fonts/` directory

## Project Structure

- `lib/`
  - `l10n/`: Localization files
  - `providers/`: State management
  - `screens/`: App screens
  - `theme/`: App theme configuration
  - `widgets/`: Reusable UI components
  - `main.dart`: Entry point

## RTL Support

The app automatically adjusts its layout direction based on the selected language. When Arabic is selected, the app switches to RTL mode, which affects:

- Text alignment
- Layout direction
- Icon placement
- Scrolling direction

## Language Switching

Use the floating action button to toggle between English and Arabic languages. The app will save your language preference and restore it when you reopen the app.
